/* Basic styles for the legal document assistant */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>l, sans-serif;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Guest mode specific styles */
.guest-mode-indicator {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  padding: 8px 16px;
  text-align: center;
  font-size: 0.9em;
  font-weight: 500;
}

.guest-session-warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #856404;
  padding: 12px;
  border-radius: 8px;
  margin: 10px 0;
  font-size: 0.9em;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .auth-card {
    margin: 20px;
    padding: 30px;
  }
  
  #main-app {
    flex-direction: column;
  }
  
  #sidebar {
    width: 100%;
    height: auto;
    flex-direction: row;
    overflow-x: auto;
  }
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.error-message {
  background: rgba(245, 101, 101, 0.1);
  border: 1px solid rgba(245, 101, 101, 0.3);
  color: #c53030;
  padding: 12px;
  border-radius: 8px;
  margin: 10px 0;
  font-size: 0.9em;
}

/* Success states */
.success-message {
  background: rgba(72, 187, 120, 0.1);
  border: 1px solid rgba(72, 187, 120, 0.3);
  color: #2f855a;
  padding: 12px;
  border-radius: 8px;
  margin: 10px 0;
  font-size: 0.9em;
}
