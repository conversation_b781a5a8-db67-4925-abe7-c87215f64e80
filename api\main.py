import os
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from contextlib import asynccontextmanager

from api.routers.agent import router as agent_router
from api.database import init_db, close_db


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_db()
    yield
    # Shutdown
    await close_db()

app = FastAPI(
    title="AgreeUpon API",
    version="2.0.0",
    description="Stateless Agentic AI-Powered Legal Document Drafter",
    lifespan=lifespan,
)

# ─────────────────────────────
# 🔐 CORS Setup
# ─────────────────────────────
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Set allowed frontend URLs in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ─────────────────────────────
# 📁 Static File Mount
# ─────────────────────────────
# Workaround: locate static dir relative to this main.py file
import pathlib

BASE_DIR = pathlib.Path(__file__).parent.parent.resolve()
STATIC_DIR = str(BASE_DIR / "static")

if not os.path.isdir(STATIC_DIR):
    os.makedirs(STATIC_DIR)

app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")


# ─────────────────────────────
# 🏠 Serve index.html at "/"
# ─────────────────────────────
@app.get("/", include_in_schema=False)
def serve_homepage():
    index_path = os.path.join(STATIC_DIR, "index.html")
    if not os.path.isfile(index_path):
        return {"error": "index.html not found"}
    return FileResponse(index_path)


# ─────────────────────────────
# 🚀 Include Agent Router
# ─────────────────────────────
app.include_router(agent_router, tags=["agent"])  # already has prefix="/agent"
