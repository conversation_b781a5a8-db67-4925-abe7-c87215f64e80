<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Agree Upon – Legal Drafting Assistant</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=JetBrains+Mono:wght@400;500&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #2d3748;
        line-height: 1.6;
      }

      .container {
        display: flex;
        height: 100vh;
        max-width: 1400px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      /* Header */
      .header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 10;
        border-radius: 20px 20px 0 0;
      }

      .header h1 {
        font-size: 1.5em;
        font-weight: 600;
      }

      .header .subtitle {
        font-size: 0.9em;
        opacity: 0.9;
      }

      .header-actions {
        display: flex;
        gap: 15px;
        align-items: center;
      }

      .new-chat-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9em;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .new-chat-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      .clear-btn {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9em;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .clear-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      /* Main Content Area */
      .main-content {
        display: flex;
        flex: 1;
        margin-top: 70px; /* Account for header */
        min-height: 0;
      }

      /* Chat Section */
      .chat-section {
        flex: 1;
        background: #fff;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #e2e8f0;
        min-width: 0;
      }

      .chat-container {
        flex: 1;
        overflow-y: auto;
        padding: 30px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .message {
        padding: 16px 20px;
        border-radius: 18px;
        max-width: 75%;
        word-break: break-word;
        font-size: 1em;
        line-height: 1.5;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        animation: fadeIn 0.3s ease-in;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .message.user {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        align-self: flex-end;
        margin-left: auto;
      }

      .message.assistant {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        color: #2d3748;
        align-self: flex-start;
        margin-right: auto;
      }

      .message.loading {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        color: #718096;
        align-self: flex-start;
        margin-right: auto;
        font-style: italic;
      }

      .input-section {
        display: flex;
        border-top: 1px solid #e2e8f0;
        padding: 20px;
        background: #f9fafb;
        gap: 15px;
      }

      .message-input {
        flex: 1;
        font-size: 1em;
        padding: 14px 18px;
        border-radius: 12px;
        border: 2px solid #e2e8f0;
        background: #fff;
        transition: all 0.3s ease;
        font-family: inherit;
        resize: none;
        min-height: 50px;
        max-height: 120px;
      }

      .message-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .send-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        border: none;
        padding: 14px 24px;
        border-radius: 12px;
        font-size: 1em;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: inherit;
        align-self: flex-end;
      }

      .send-button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
      }

      .send-button:disabled {
        background: #a0aec0;
        transform: none;
        box-shadow: none;
        cursor: not-allowed;
      }

      /* Document Section */
      .document-section {
        flex: 1;
        background: #f9fafb;
        padding: 30px 25px;
        display: flex;
        flex-direction: column;
        min-width: 0;
      }

      .document-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .document-header h2 {
        margin: 0;
        font-size: 1.3em;
        font-weight: 600;
        color: #2d3748;
      }

      .document-actions {
        display: flex;
        gap: 10px;
      }

      .document-actions button {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: #fff;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9em;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .document-actions button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
      }

      .document-content {
        flex: 1;
        background: #fff;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        overflow-y: auto;
        font-family: "JetBrains Mono", "Fira Code", monospace;
        font-size: 0.95em;
        color: #2d3748;
        white-space: pre-wrap;
        line-height: 1.6;
        min-height: 200px;
      }

      .document-placeholder {
        color: #a0aec0;
        font-style: italic;
        text-align: center;
        padding: 40px 20px;
        font-family: "Inter", sans-serif;
      }

      /* Welcome Message */
      .welcome-message {
        text-align: center;
        padding: 40px 20px;
        color: #718096;
        font-size: 1.1em;
      }

      .welcome-message h3 {
        color: #2d3748;
        margin-bottom: 15px;
        font-size: 1.3em;
      }

      .welcome-examples {
        margin-top: 20px;
        text-align: left;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
      }

      .welcome-examples h4 {
        color: #4a5568;
        margin-bottom: 10px;
        font-size: 1em;
      }

      .welcome-examples ul {
        list-style: none;
        padding: 0;
      }

      .welcome-examples li {
        padding: 8px 0;
        color: #667eea;
        cursor: pointer;
        transition: color 0.3s ease;
      }

      .welcome-examples li:hover {
        color: #764ba2;
        text-decoration: underline;
      }

      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .container {
          margin: 10px;
          border-radius: 15px;
        }

        .main-content {
          flex-direction: column;
        }

        .chat-section,
        .document-section {
          flex: 1;
          min-height: 300px;
        }

        .header {
          flex-direction: column;
          gap: 10px;
          text-align: center;
        }

        .header-actions {
          justify-content: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Header -->
      <div class="header">
        <div>
          <h1>Agree Upon</h1>
          <div class="subtitle">Legal Drafting Assistant</div>
        </div>
        <div class="header-actions">
          <button class="new-chat-btn" onclick="startNewChat()">
            🆕 New Chat
          </button>
          <button class="clear-btn" onclick="clearAll()">🗑️ Clear All</button>
        </div>
      </div>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Chat Section -->
        <div class="chat-section">
          <div class="chat-container" id="chatContainer">
            <div class="welcome-message">
              <h3>Welcome to Agree Upon</h3>
              <p>
                I'm your AI legal assistant. I can help you draft various legal
                documents.
              </p>

              <div class="welcome-examples">
                <h4>Try asking me to create:</h4>
                <ul>
                  <li onclick="sendExampleMessage('Create an NDA agreement')">
                    📄 NDA Agreement
                  </li>
                  <li onclick="sendExampleMessage('Draft a lease agreement')">
                    🏠 Lease Agreement
                  </li>
                  <li
                    onclick="sendExampleMessage('Create a partnership agreement')"
                  >
                    🤝 Partnership Agreement
                  </li>
                  <li
                    onclick="sendExampleMessage('Draft a shareholder agreement')"
                  >
                    📊 Shareholder Agreement
                  </li>
                  <li
                    onclick="sendExampleMessage('Create an employment contract')"
                  >
                    💼 Employment Contract
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="input-section">
            <textarea
              class="message-input"
              id="messageInput"
              placeholder="Type your message here... (e.g., 'Create an NDA agreement')"
              rows="1"
            ></textarea>
            <button class="send-button" id="sendButton" onclick="sendMessage()">
              Send
            </button>
          </div>
        </div>

        <!-- Document Section -->
        <div class="document-section">
          <div class="document-header">
            <h2>Generated Document</h2>
            <div class="document-actions">
              <button onclick="copyDocument()">📋 Copy</button>
              <button onclick="downloadDocument()">💾 Download</button>
            </div>
          </div>

          <div class="document-content" id="documentContent">
            <div class="document-placeholder">
              No document generated yet. Start a conversation to create your
              legal document.
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global state
      let agentState = null;
      let conversationHistory = [];
      let currentDocument = "";
      let isLoading = false;
      let sessionId = null;

      // DOM Elements
      const chatContainer = document.getElementById("chatContainer");
      const messageInput = document.getElementById("messageInput");
      const sendButton = document.getElementById("sendButton");
      const documentContent = document.getElementById("documentContent");

      // Initialize session ID
      sessionId = Date.now().toString();

      // API Functions
      async function sendMessageToAgent(userInput) {
        const response = await fetch("/agent/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            session_id: sessionId,
          },
          body: JSON.stringify({
            user_input: userInput,
            agent_state: agentState,
            conversation_history: conversationHistory,
            document: currentDocument,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
      }

      // UI Functions
      function addMessage(role, content) {
        const messageDiv = document.createElement("div");
        messageDiv.className = `message ${role}`;
        messageDiv.textContent = content;

        // Remove welcome message if it exists
        const welcomeMessage = chatContainer.querySelector(".welcome-message");
        if (welcomeMessage) {
          welcomeMessage.remove();
        }

        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }

      function updateDocument(content) {
        if (content && content.trim()) {
          documentContent.innerHTML = `<pre>${content}</pre>`;
          currentDocument = content;
        } else {
          documentContent.innerHTML = `
            <div class="document-placeholder">
              No document generated yet. Start a conversation to create your legal document.
            </div>
          `;
        }
      }

      function setLoading(loading) {
        isLoading = loading;
        sendButton.disabled = loading;
        messageInput.disabled = loading;

        if (loading) {
          sendButton.textContent = "Sending...";
          addMessage("loading", "AI is thinking...");
        } else {
          sendButton.textContent = "Send";
          // Remove loading message
          const loadingMsg = chatContainer.querySelector(".message.loading");
          if (loadingMsg) {
            loadingMsg.remove();
          }
        }
      }

      // Main Functions
      async function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || isLoading) return;

        // Add user message to chat
        addMessage("user", message);
        messageInput.value = "";

        // Auto-resize textarea
        messageInput.style.height = "auto";

        setLoading(true);

        try {
          const response = await sendMessageToAgent(message);

          // Update state
          agentState = response.agent_state;
          conversationHistory = response.conversation_history;

          // Add assistant response
          addMessage("assistant", response.agent_reply);

          // Update document if provided
          if (response.document) {
            updateDocument(response.document);
          }
        } catch (error) {
          console.error("Error sending message:", error);
          addMessage(
            "assistant",
            "Sorry, I encountered an error. Please try again."
          );
        } finally {
          setLoading(false);
        }
      }

      function sendExampleMessage(message) {
        messageInput.value = message;
        sendMessage();
      }

      // Utility Functions
      function startNewChat() {
        // Reset state
        agentState = null;
        conversationHistory = [];
        currentDocument = "";
        sessionId = Date.now().toString();

        // Clear chat
        chatContainer.innerHTML = `
          <div class="welcome-message">
            <h3>Welcome to Agree Upon</h3>
            <p>I'm your AI legal assistant. I can help you draft various legal documents.</p>

            <div class="welcome-examples">
              <h4>Try asking me to create:</h4>
              <ul>
                <li onclick="sendExampleMessage('Create an NDA agreement')">📄 NDA Agreement</li>
                <li onclick="sendExampleMessage('Draft a lease agreement')">🏠 Lease Agreement</li>
                <li onclick="sendExampleMessage('Create a partnership agreement')">🤝 Partnership Agreement</li>
                <li onclick="sendExampleMessage('Draft a shareholder agreement')">📊 Shareholder Agreement</li>
                <li onclick="sendExampleMessage('Create an employment contract')">💼 Employment Contract</li>
              </ul>
            </div>
          </div>
        `;

        // Clear document
        updateDocument("");
      }

      function clearAll() {
        if (
          confirm(
            "Are you sure you want to clear all conversation history and documents?"
          )
        ) {
          startNewChat();
        }
      }

      function copyDocument() {
        if (currentDocument) {
          navigator.clipboard
            .writeText(currentDocument)
            .then(() => {
              // Show temporary feedback
              const button = event.target;
              const originalText = button.textContent;
              button.textContent = "✅ Copied!";
              setTimeout(() => {
                button.textContent = originalText;
              }, 2000);
            })
            .catch((err) => {
              console.error("Failed to copy: ", err);
              alert("Failed to copy document to clipboard");
            });
        } else {
          alert("No document to copy");
        }
      }

      function downloadDocument() {
        if (currentDocument) {
          const blob = new Blob([currentDocument], { type: "text/plain" });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "legal-document.txt";
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        } else {
          alert("No document to download");
        }
      }

      // Event Handlers
      messageInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });

      // Auto-resize textarea
      messageInput.addEventListener("input", () => {
        messageInput.style.height = "auto";
        messageInput.style.height = messageInput.scrollHeight + "px";
      });

      // Initialize
      document.addEventListener("DOMContentLoaded", () => {
        messageInput.focus();
      });
    </script>
  </body>
</html>
