import os
from tortoise import Tortoise
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DATABASE_CONFIG = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.mysql",
            "credentials": {
                "host": os.getenv("HOST_NAME"),
                "port": 3306,
                "user": os.getenv("DB_USER"),
                "password": os.getenv("DB_PASSWORD"),
                "database": os.getenv("DATABASE"),
            }
        }
    },
    "apps": {
        "models": {
            "models": ["api.models"],
            "default_connection": "default",
        }
    }
}

async def init_db():
    """Initialize database connection"""
    await Tortoise.init(config=DATABASE_CONFIG)
    # Check if tables exist before creating them
    await <PERSON>toise.generate_schemas(safe=True)

async def close_db():
    """Close database connection"""
    await Tortoise.close_connections()
