"""
agent/performance.py
────────────────────────────────────────────────────────────────────────────────
Performance monitoring and optimization utilities for the legal agent.
"""

import time
import logging
from functools import wraps
from typing import Dict, Any, Callable
from collections import defaultdict, deque

logger = logging.getLogger("agent.performance")

class PerformanceMonitor:
    """Centralized performance monitoring for the agent."""
    
    def __init__(self, max_history: int = 100):
        self.max_history = max_history
        self.metrics = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'recent_times': deque(maxlen=max_history),
            'error_count': 0,
            'success_rate': 100.0
        })
        
    def record_call(self, function_name: str, duration: float, success: bool = True):
        """Record a function call with timing and success information."""
        metric = self.metrics[function_name]
        
        metric['call_count'] += 1
        if success:
            metric['total_time'] += duration
            metric['avg_time'] = metric['total_time'] / metric['call_count']
            metric['min_time'] = min(metric['min_time'], duration)
            metric['max_time'] = max(metric['max_time'], duration)
            metric['recent_times'].append(duration)
        else:
            metric['error_count'] += 1
            
        # Calculate success rate
        metric['success_rate'] = ((metric['call_count'] - metric['error_count']) / metric['call_count']) * 100
        
    def get_stats(self, function_name: str = None) -> Dict[str, Any]:
        """Get performance statistics."""
        if function_name:
            return dict(self.metrics.get(function_name, {}))
        return {name: dict(stats) for name, stats in self.metrics.items()}
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all performance metrics."""
        total_calls = sum(m['call_count'] for m in self.metrics.values())
        total_errors = sum(m['error_count'] for m in self.metrics.values())
        avg_success_rate = sum(m['success_rate'] for m in self.metrics.values()) / len(self.metrics) if self.metrics else 100.0
        
        return {
            'total_functions_monitored': len(self.metrics),
            'total_calls': total_calls,
            'total_errors': total_errors,
            'overall_success_rate': avg_success_rate,
            'slowest_functions': self._get_slowest_functions(),
            'most_called_functions': self._get_most_called_functions()
        }
    
    def _get_slowest_functions(self, limit: int = 5) -> list:
        """Get the slowest functions by average time."""
        return sorted(
            [(name, stats['avg_time']) for name, stats in self.metrics.items() if stats['call_count'] > 0],
            key=lambda x: x[1],
            reverse=True
        )[:limit]
    
    def _get_most_called_functions(self, limit: int = 5) -> list:
        """Get the most frequently called functions."""
        return sorted(
            [(name, stats['call_count']) for name, stats in self.metrics.items()],
            key=lambda x: x[1],
            reverse=True
        )[:limit]

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def monitor_performance(func_name: str = None):
    """Decorator to monitor function performance."""
    def decorator(func: Callable) -> Callable:
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                performance_monitor.record_call(name, duration, success)
                
                # Log slow calls
                if duration > 5.0:
                    logger.warning(f"Slow call detected: {name} took {duration:.2f}s")
                    
        return wrapper
    return decorator

def get_performance_report() -> str:
    """Generate a human-readable performance report."""
    summary = performance_monitor.get_summary()
    
    report = [
        "🔍 Agent Performance Report",
        "=" * 40,
        f"Functions Monitored: {summary['total_functions_monitored']}",
        f"Total Calls: {summary['total_calls']}",
        f"Total Errors: {summary['total_errors']}",
        f"Overall Success Rate: {summary['overall_success_rate']:.1f}%",
        "",
        "🐌 Slowest Functions:",
    ]
    
    for name, avg_time in summary['slowest_functions']:
        report.append(f"  {name}: {avg_time:.3f}s avg")
    
    report.extend([
        "",
        "📞 Most Called Functions:",
    ])
    
    for name, call_count in summary['most_called_functions']:
        report.append(f"  {name}: {call_count} calls")
    
    return "\n".join(report)

def log_performance_summary():
    """Log a performance summary to the logger."""
    logger.info("\n" + get_performance_report())

# Optimization suggestions based on performance data
def get_optimization_suggestions() -> list:
    """Analyze performance data and suggest optimizations."""
    suggestions = []
    summary = performance_monitor.get_summary()
    
    # Check for slow functions
    for name, avg_time in summary['slowest_functions']:
        if avg_time > 3.0:
            suggestions.append(f"Consider optimizing {name} (avg: {avg_time:.2f}s)")
    
    # Check for high error rates
    for name, stats in performance_monitor.metrics.items():
        if stats['success_rate'] < 90.0 and stats['call_count'] > 5:
            suggestions.append(f"High error rate in {name} ({stats['success_rate']:.1f}% success)")
    
    # Check for memory-intensive operations
    if summary['total_calls'] > 100:
        suggestions.append("Consider implementing caching for frequently called functions")
    
    return suggestions

def reset_performance_stats():
    """Reset all performance statistics."""
    performance_monitor.metrics.clear()
    logger.info("Performance statistics reset")
