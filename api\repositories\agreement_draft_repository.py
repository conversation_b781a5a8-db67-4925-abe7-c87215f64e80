"""
Agreement Draft Repository
Handles data access operations for agreement drafts
"""

import logging
from typing import Optional, List
from api.models import AgreementDraft

logger = logging.getLogger(__name__)


class AgreementDraftRepository:
    """Repository layer for agreement draft data access"""
    
    async def create_draft(
        self, 
        content: str, 
        usr_id: Optional[int] = None, 
        session_id: Optional[int] = None
    ) -> AgreementDraft:
        """
        Create a new agreement draft
        
        Args:
            content: The draft content
            usr_id: User ID (optional)
            session_id: Session ID (optional)
            
        Returns:
            AgreementDraft: The created draft object
        """
        try:
            draft = await AgreementDraft.create(
                content=content,
                usr_id=usr_id,
                session_id=session_id
            )
            logger.debug(f"Created draft with ID: {draft.draft_id}")
            return draft
        except Exception as e:
            logger.error(f"Error creating draft: {str(e)}")
            raise
    
    async def get_by_id(self, draft_id: int) -> Optional[AgreementDraft]:
        """
        Get draft by ID
        
        Args:
            draft_id: The draft ID
            
        Returns:
            AgreementDraft or None if not found
        """
        try:
            return await AgreementDraft.get_or_none(draft_id=draft_id)
        except Exception as e:
            logger.error(f"Error retrieving draft {draft_id}: {str(e)}")
            raise
    
    async def get_by_session_id(self, session_id: int) -> List[AgreementDraft]:
        """
        Get all drafts for a session
        
        Args:
            session_id: The session ID
            
        Returns:
            List of AgreementDraft objects
        """
        try:
            return await AgreementDraft.filter(session_id=session_id).order_by('-created_at')
        except Exception as e:
            logger.error(f"Error retrieving drafts for session {session_id}: {str(e)}")
            raise
    
    async def get_by_user_id(self, usr_id: int) -> List[AgreementDraft]:
        """
        Get all drafts for a user
        
        Args:
            usr_id: The user ID
            
        Returns:
            List of AgreementDraft objects
        """
        try:
            return await AgreementDraft.filter(usr_id=usr_id).order_by('-created_at')
        except Exception as e:
            logger.error(f"Error retrieving drafts for user {usr_id}: {str(e)}")
            raise
    
    async def update_content(self, draft_id: int, content: str) -> Optional[AgreementDraft]:
        """
        Update draft content
        
        Args:
            draft_id: The draft ID
            content: New content
            
        Returns:
            Updated AgreementDraft or None if not found
        """
        try:
            draft = await AgreementDraft.get_or_none(draft_id=draft_id)
            if draft:
                draft.content = content
                await draft.save()
                logger.debug(f"Updated draft {draft_id}")
                return draft
            return None
        except Exception as e:
            logger.error(f"Error updating draft {draft_id}: {str(e)}")
            raise
    
    async def delete_draft(self, draft_id: int) -> bool:
        """
        Delete a draft
        
        Args:
            draft_id: The draft ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        try:
            draft = await AgreementDraft.get_or_none(draft_id=draft_id)
            if draft:
                await draft.delete()
                logger.debug(f"Deleted draft {draft_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting draft {draft_id}: {str(e)}")
            raise
    
    async def get_latest_by_session(self, session_id: int) -> Optional[AgreementDraft]:
        """
        Get the latest draft for a session
        
        Args:
            session_id: The session ID
            
        Returns:
            AgreementDraft or None if not found
        """
        try:
            return await AgreementDraft.filter(session_id=session_id).order_by('-created_at').first()
        except Exception as e:
            logger.error(f"Error retrieving latest draft for session {session_id}: {str(e)}")
            raise
