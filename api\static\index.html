<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Legal Document Assistant</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    /* small styling for the response time */
    .message.assistant .response-time {
      font-size: 0.8em;
      color: #888;
      margin-left: 6px;
    }
  </style>
</head>
<body>

<!-- ─── Auth overlay ─────────────────────────────────────────── -->
<div id="authOverlay" class="overlay">
  <div class="auth-box">
    <form id="signupForm" class="auth-form">
      <h2>Sign Up</h2>
      <input type="text" id="signupUsername" placeholder="Username" required />
      <input type="email" id="signupEmail" placeholder="Email" required />
      <input type="password" id="signupPassword" placeholder="Password" required />
      <button type="submit">Register</button>
      <p>Already have an account? <a href="#" id="showLogin">Log in</a></p>
    </form>

    <form id="loginForm" class="auth-form hidden">
      <h2>Log In</h2>
      <input type="text" id="loginUsername" placeholder="Username" required />
      <input type="password" id="loginPassword" placeholder="Password" required />
      <button type="submit">Login</button>
      <p>Don't have an account? <a href="#" id="showSignup">Sign up</a></p>
    </form>
  </div>
</div>

<!-- ─── Main app ─────────────────────────────────────────────── -->
<div id="app" class="hidden">
  <div id="sidebar">
    <button id="btnNew">+ New Conversation</button>
    <ul id="convList"></ul>
  </div>

  <div id="chat">
    <div id="messages"></div>
    <div class="input-row">
      <input id="input" type="text" placeholder="Ask a legal question…" />
      <button id="btnSend">Send</button>
    </div>
  </div>

  <div id="doc">
    <h3>Generated&nbsp;Document</h3>
    <div id="documentContent">No document generated yet.</div>
  </div>
</div>

<script src="app.js"></script>
</body>
</html>
