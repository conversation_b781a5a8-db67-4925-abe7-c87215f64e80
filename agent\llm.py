import os
import logging
from dotenv import load_dotenv
from fastapi import HTTPException
from httpx import HTTPStatusError
from langchain_core.runnables.base import Runnable
from langchain_openai import ChatOpenAI

# ────────────────────────────────
# 🛠️ Logging Setup
# ────────────────────────────────
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("agent.llm")
logger.setLevel(logging.INFO)


# Display logs in terminal
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

formatter = logging.Formatter("[%(levelname)s] %(message)s")
console_handler.setFormatter(formatter)

logger.addHandler(console_handler)

# ────────────────────────────────
# 🔐 Load Environment Variables
# ────────────────────────────────
load_dotenv()
HF_TOKEN = os.getenv("HF_TOKEN")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")

if not HF_TOKEN:
    raise RuntimeError("Missing HF_TOKEN environment variable")
if not DEEPSEEK_API_KEY:
    raise RuntimeError("Missing DEEPSEEK_API_KEY environment variable")

# ────────────────────────────────
# 🤖 LLM Configurations
# ────────────────────────────────
llm_chain: Runnable = ChatOpenAI(
    model="deepseek-chat",
    openai_api_base="https://oaitzvyxm6614ekn.us-east-1.aws.endpoints.huggingface.cloud/v1/",
    openai_api_key=HF_TOKEN,
    temperature=0.3,
    max_tokens=8192,
    streaming=True,
)

llm_chain_deepseek: Runnable = ChatOpenAI(
    model="deepseek-chat",
    openai_api_base="https://api.deepseek.com/v1",
    openai_api_key=DEEPSEEK_API_KEY,
    temperature=0.3,
    max_tokens=8192,
    streaming=True,
)

TOGGLE_LLM = False


class HybridLLM(Runnable):
    """Optimized hybrid LLM with intelligent fallback and caching."""

    def __init__(self):
        self._last_successful_llm = None
        self._failure_count = {"primary": 0, "secondary": 0}
        self._max_failures = 3

    def invoke(self, input, config=None, **kwargs):
        # Smart LLM selection based on recent performance
        if not TOGGLE_LLM:
            logger.debug("Using Secondary LLM (TOGGLE_LLM=False)")
            return self._invoke_with_fallback(
                llm_chain_deepseek, "secondary", input, config, **kwargs
            )

        # Try last successful LLM first if available
        if (
            self._last_successful_llm == "secondary"
            and self._failure_count["primary"] >= 2
        ):
            logger.debug("Using Secondary LLM (recent primary failures)")
            return self._invoke_with_fallback(
                llm_chain_deepseek, "secondary", input, config, **kwargs
            )

        # Default: try primary first
        logger.debug("Trying Primary LLM")
        return self._invoke_with_fallback(llm_chain, "primary", input, config, **kwargs)

    def _invoke_with_fallback(self, llm, llm_type, input, config=None, **kwargs):
        """Invoke LLM with intelligent fallback."""
        try:
            result = llm.invoke(input, config=config, **kwargs)
            # Reset failure count on success
            self._failure_count[llm_type] = 0
            self._last_successful_llm = llm_type
            return result

        except Exception as e:
            self._failure_count[llm_type] += 1
            logger.warning(f"{llm_type.title()} LLM failed: {str(e)[:100]}...")

            # Try fallback if primary failed
            if llm_type == "primary":
                logger.info("Falling back to Secondary LLM")
                try:
                    result = llm_chain_deepseek.invoke(input, config=config, **kwargs)
                    self._last_successful_llm = "secondary"
                    return result
                except Exception as fallback_e:
                    self._failure_count["secondary"] += 1
                    logger.error(
                        f"Both LLMs failed. Secondary error: {str(fallback_e)[:100]}..."
                    )
                    raise fallback_e
            else:
                # Secondary LLM failed, no fallback available
                raise e
