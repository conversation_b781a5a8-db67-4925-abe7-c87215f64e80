"""
agent/memory.py
─────────────────────────────────────────────────────────────────────────────
Simple stateless memory system that works with passed conversation history.
"""

from __future__ import annotations

import logging
from functools import lru_cache
from typing import Any, Dict, List

from langchain.memory import ConversationBufferMemory

logger = logging.getLogger("agent.memory")
logger.setLevel(logging.DEBUG)


# ─────────────────────────────────────────────────────────────
#  Simple stateless memory class
# ─────────────────────────────────────────────────────────────
class StatelessB<PERSON><PERSON><PERSON><PERSON><PERSON>(ConversationBufferM<PERSON><PERSON>):
    """
    Optimized <PERSON><PERSON><PERSON><PERSON> Convers<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> with caching and performance improvements.
    No persistence - purely stateless operation.
    """

    def __init__(self, conversation_history: List[Dict[str, str]] = None):
        super().__init__(
            memory_key="history",
            input_key="user_input",
            output_key="text",
            return_messages=True,
        )

        # Track memory stats
        self._message_count = 0
        self._max_messages = 50  # Limit memory size for performance

        if conversation_history:
            self._bootstrap_from_history(conversation_history)

    def _bootstrap_from_history(
        self, conversation_history: List[Dict[str, str]]
    ) -> None:
        """Load conversation history into memory."""
        logger.debug(
            "🧩 Bootstrapping %s messages from history", len(conversation_history)
        )

        # Limit history size for performance
        limited_history = (
            conversation_history[-self._max_messages :]
            if len(conversation_history) > self._max_messages
            else conversation_history
        )

        for msg in limited_history:
            sender = msg.get("sender", "")
            content = msg.get("content", "")

            if sender == "user":
                self.chat_memory.add_user_message(content)
                self._message_count += 1
            elif sender == "assistant":
                self.chat_memory.add_ai_message(content)
                self._message_count += 1
            else:
                logger.warning("Unknown sender: %s", sender)

    def save_context(self, inputs: Dict[str, Any], outputs: Dict[str, str]) -> None:
        """Update in-memory buffer only - no persistence."""
        super().save_context(inputs, outputs)
        logger.debug("💾 Updated memory buffer")


# ─────────────────────────────────────────────────────────────
#  Factory for agent_runner
# ─────────────────────────────────────────────────────────────
def get_memory(
    conversation_history: List[Dict[str, str]] = None,
) -> ConversationBufferMemory:
    """
    Return stateless memory initialized with conversation history.
    """
    return StatelessBufferMemory(conversation_history=conversation_history)
