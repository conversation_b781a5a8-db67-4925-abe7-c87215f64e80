# AgreeUpon Legal AI API Documentation

## Overview

The AgreeUpon Legal AI API provides intelligent legal document drafting and conversation capabilities. This stateless API helps users create, review, and refine legal documents through natural language interactions.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, no authentication is required for API access.

## Content Type

All requests and responses use `application/json`.

---

## Endpoints

### 1. Health Check

**GET** `/health`

Check if the API service is running.

#### Response

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

### 2. Agent Chat

**POST** `/agent/chat`

Main endpoint for interacting with the legal AI agent.

#### Request Body

```json
{
  "user_input": "string",
  "agent_state": "object|null",
  "conversation_history": "array|null",
  "document": "string"
}
```

#### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `user_input` | string | Yes | User's message or question |
| `agent_state` | object\|null | No | Current agent state (null for new conversations) |
| `conversation_history` | array\|null | No | Previous conversation messages (null for new conversations) |
| `document` | string | No | Current document content (empty string if none) |

#### Response Body

```json
{
  "agent_reply": "string",
  "agent_state": "object",
  "conversation_history": "array",
  "document": "string"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `agent_reply` | string | AI agent's response to the user |
| `agent_state` | object | Updated agent state with document progress |
| `conversation_history` | array | Complete conversation history including new messages |
| `document` | string | Generated or updated document content |

---

## Data Models

### Agent State Object

```json
{
  "document_type": "string",
  "is_drafted": "boolean",
  "needed_fields": "object",
  "draft": "string"
}
```

| Field | Type | Description |
|-------|------|-------------|
| `document_type` | string | Type of document being created (e.g., "NDA", "Contract") |
| `is_drafted` | boolean | Whether a complete draft has been generated |
| `needed_fields` | object | Key-value pairs of required document fields |
| `draft` | string | Current document draft content |

### Conversation History Array

```json
[
  {
    "sender": "user",
    "content": "I need to create an NDA agreement"
  },
  {
    "sender": "assistant",
    "content": "I'll help you create an NDA agreement..."
  }
]
```

| Field | Type | Description |
|-------|------|-------------|
| `sender` | string | Either "user" or "assistant" |
| `content` | string | Message content |

---

## Example Usage

### Starting a New Conversation

**Request:**
```json
POST /agent/chat
{
  "user_input": "I need to create an NDA agreement",
  "agent_state": null,
  "conversation_history": null,
  "document": ""
}
```

**Response:**
```json
{
  "agent_reply": "I'll help you create an NDA agreement. To get started, could you please provide some key details? For example: 1) Who are the parties involved (names of disclosing and receiving parties)? 2) What is the purpose of the NDA? 3) How long should the confidentiality period last?",
  "agent_state": {
    "document_type": "NDA",
    "is_drafted": false,
    "needed_fields": {},
    "draft": ""
  },
  "conversation_history": [
    {
      "sender": "user",
      "content": "I need to create an NDA agreement"
    },
    {
      "sender": "assistant",
      "content": "I'll help you create an NDA agreement. To get started, could you please provide some key details? For example: 1) Who are the parties involved (names of disclosing and receiving parties)? 2) What is the purpose of the NDA? 3) How long should the confidentiality period last?"
    }
  ],
  "document": ""
}
```

### Continuing a Conversation

**Request:**
```json
POST /agent/chat
{
  "user_input": "The disclosing party is Alice Corp and the receiving party is Bob LLC. The purpose is to discuss a potential partnership.",
  "agent_state": {
    "document_type": "NDA",
    "is_drafted": false,
    "needed_fields": {},
    "draft": ""
  },
  "conversation_history": [
    {
      "sender": "user",
      "content": "I need to create an NDA agreement"
    },
    {
      "sender": "assistant",
      "content": "I'll help you create an NDA agreement. To get started, could you please provide some key details?"
    }
  ],
  "document": ""
}
```

**Response:**
```json
{
  "agent_reply": "Perfect! I have the parties and purpose. Now I need a few more details: 1) How long should the confidentiality period last? (e.g., 2 years, 5 years, indefinitely) 2) What is the effective date for this agreement? 3) Which state's laws should govern this agreement?",
  "agent_state": {
    "document_type": "NDA",
    "is_drafted": false,
    "needed_fields": {
      "Disclosing Party": "Alice Corp",
      "Receiving Party": "Bob LLC",
      "Purpose": "to discuss a potential partnership"
    },
    "draft": ""
  },
  "conversation_history": [
    {
      "sender": "user",
      "content": "I need to create an NDA agreement"
    },
    {
      "sender": "assistant",
      "content": "I'll help you create an NDA agreement. To get started, could you please provide some key details?"
    },
    {
      "sender": "user",
      "content": "The disclosing party is Alice Corp and the receiving party is Bob LLC. The purpose is to discuss a potential partnership."
    },
    {
      "sender": "assistant",
      "content": "Perfect! I have the parties and purpose. Now I need a few more details: 1) How long should the confidentiality period last? 2) What is the effective date for this agreement? 3) Which state's laws should govern this agreement?"
    }
  ],
  "document": ""
}
```

### Document Generation

When all required fields are collected, the agent will generate a document:

**Response with Generated Document:**
```json
{
  "agent_reply": "I've created your NDA agreement with all the details you provided. Please review the document below and let me know if you'd like any changes.",
  "agent_state": {
    "document_type": "NDA",
    "is_drafted": true,
    "needed_fields": {
      "Disclosing Party": "Alice Corp",
      "Receiving Party": "Bob LLC",
      "Purpose": "to discuss a potential partnership",
      "Confidentiality Period": "2 years",
      "Effective Date": "January 15, 2024",
      "Governing Law": "California"
    },
    "draft": "NON-DISCLOSURE AGREEMENT\n\nThis Non-Disclosure Agreement..."
  },
  "conversation_history": [...],
  "document": "NON-DISCLOSURE AGREEMENT\n\nThis Non-Disclosure Agreement (\"Agreement\") is entered into on January 15, 2024, between Alice Corp (\"Disclosing Party\") and Bob LLC (\"Receiving Party\")...\n\n[Complete document content]"
}
```

---

## Error Handling

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input data |
| 422 | Validation Error - Request body validation failed |
| 500 | Internal Server Error |

### Error Response Format

```json
{
  "detail": "Error description"
}
```

### Common Errors

#### 400 Bad Request
```json
{
  "detail": "Invalid request format"
}
```

#### 422 Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "user_input"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

---

## Integration Guidelines

### Frontend Integration

1. **Initialize Conversation**: Send request with `null` values for `agent_state` and `conversation_history`

2. **Maintain State**: Always include the latest `agent_state` and `conversation_history` in subsequent requests

3. **Handle Responses**: Update your UI with the `agent_reply` and display the `document` when available

4. **Error Handling**: Implement proper error handling for network issues and API errors

### Example JavaScript Integration

```javascript
class AgreeUponAPI {
  constructor(baseURL = 'http://localhost:8000') {
    this.baseURL = baseURL;
    this.agentState = null;
    this.conversationHistory = null;
    this.currentDocument = '';
  }

  async sendMessage(userInput) {
    const response = await fetch(`${this.baseURL}/agent/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_input: userInput,
        agent_state: this.agentState,
        conversation_history: this.conversationHistory,
        document: this.currentDocument
      })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();

    // Update internal state
    this.agentState = data.agent_state;
    this.conversationHistory = data.conversation_history;
    this.currentDocument = data.document;

    return data;
  }
}
```

---

## Rate Limiting

Currently, no rate limiting is implemented. For production use, consider implementing appropriate rate limiting based on your requirements.

---

## Support

For technical support or questions about the API, please refer to the project documentation or contact the development team.
```