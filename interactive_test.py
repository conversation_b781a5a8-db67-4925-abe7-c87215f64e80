#!/usr/bin/env python3
"""
Interactive testing script for AgreeUpon API
Allows you to test the API with a continuous while loop
"""

import requests
import json
import sys
from typing import Dict, List, Any, Optional


class AgreeUponTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_url = f"{base_url}/agent/chat"
        self.current_state = None
        self.conversation_history = []
        self.current_document = ""

    def print_separator(self, title: str = ""):
        print("\n" + "=" * 60)
        if title:
            print(f" {title}")
            print("=" * 60)

    def print_response(self, response_data: Dict[str, Any]):
        """Pretty print the API response"""
        self.print_separator("API RESPONSE")

        print(f"🤖 Agent Reply:")
        print(f"   {response_data.get('agent_reply', 'No reply')}")

        print(f"\n📋 Agent State:")
        state = response_data.get("agent_state", {})
        print(f"   Document Type: {state.get('document_type', 'None')}")
        print(f"   Is Drafted: {state.get('is_drafted', False)}")
        print(f"   Fields: {list(state.get('needed_fields', {}).keys())}")

        print(
            f"\n💬 Conversation History Length: {len(response_data.get('conversation_history', []))}"
        )

        document = response_data.get("document", "")
        if document:
            print(f"\n📄 Document Generated: Yes ({len(document)} characters)")
            print("   Preview:")
            preview = document[:200] + "..." if len(document) > 200 else document
            for line in preview.split("\n")[:5]:
                print(f"   {line}")
            if len(document) > 200:
                print("   ...")
        else:
            print(f"\n📄 Document Generated: No")

    def send_request(self, user_input: str) -> bool:
        """Send request to API and update internal state"""
        request_data = {
            "user_input": user_input,
            "agent_state": self.current_state,
            "conversation_history": self.conversation_history,
            "document": self.current_document,
        }

        try:
            print(f"\n📤 Sending request...")
            response = requests.post(self.api_url, json=request_data, timeout=120)

            if response.status_code == 200:
                result = response.json()
                self.print_response(result)

                # Update internal state for next request
                self.current_state = result.get("agent_state")
                self.conversation_history = result.get("conversation_history", [])
                self.current_document = result.get("document", "")

                return True
            else:
                print(f"❌ API Error ({response.status_code}): {response.text}")
                return False

        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Make sure the server is running")
            print("   Start with: uvicorn api.main:app --reload")
            return False
        except requests.exceptions.Timeout:
            print("❌ Request timed out (this can happen with document generation)")
            return False
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

    def show_current_state(self):
        """Display current conversation state"""
        self.print_separator("CURRENT STATE")

        if self.current_state:
            print(
                f"📋 Document Type: {self.current_state.get('document_type', 'None')}"
            )
            print(f"📝 Is Drafted: {self.current_state.get('is_drafted', False)}")
            fields = self.current_state.get("needed_fields", {})
            if fields:
                print(f"🔧 Collected Fields:")
                for key, value in fields.items():
                    print(f"   {key}: {value}")
            else:
                print(f"🔧 No fields collected yet")
        else:
            print("📋 No state yet - starting fresh")

        print(f"💬 Conversation turns: {len(self.conversation_history)}")

        if self.current_document:
            print(f"📄 Document: {len(self.current_document)} characters")
        else:
            print(f"📄 No document generated yet")

    def reset_session(self):
        """Reset the conversation to start fresh"""
        self.current_state = None
        self.conversation_history = []
        self.current_document = ""
        print("🔄 Session reset - starting fresh")

    def show_help(self):
        """Show available commands"""
        self.print_separator("AVAILABLE COMMANDS")
        print("📝 Just type your message to send to the agent")
        print("🔧 Special commands:")
        print("   /state    - Show current conversation state")
        print("   /reset    - Reset conversation and start fresh")
        print("   /document - Show full document content")
        print("   /history  - Show conversation history")
        print("   /help     - Show this help message")
        print("   /quit     - Exit the program")

    def show_document(self):
        """Show the full document content"""
        if self.current_document:
            self.print_separator("FULL DOCUMENT")
            print(self.current_document)
        else:
            print("📄 No document generated yet")

    def show_history(self):
        """Show conversation history"""
        if self.conversation_history:
            self.print_separator("CONVERSATION HISTORY")
            for i, msg in enumerate(self.conversation_history, 1):
                sender = msg.get("sender", "unknown")
                content = msg.get("content", "")
                icon = "🧑" if sender == "user" else "🤖"
                print(f"{i}. {icon} {sender.title()}: {content}")
        else:
            print("💬 No conversation history yet")

    def run(self):
        """Main interactive loop"""
        print("🚀 AgreeUpon API Interactive Tester")
        print("=" * 60)
        print("Type your messages to interact with the legal document agent")
        print("Type /help for available commands, /quit to exit")

        while True:
            try:
                # Get user input
                user_input = input("\n💬 You: ").strip()

                # Handle special commands
                if user_input.lower() in ["/quit", "/exit", "/q"]:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == "/help":
                    self.show_help()
                    continue
                elif user_input.lower() == "/state":
                    self.show_current_state()
                    continue
                elif user_input.lower() == "/reset":
                    self.reset_session()
                    continue
                elif user_input.lower() == "/document":
                    self.show_document()
                    continue
                elif user_input.lower() == "/history":
                    self.show_history()
                    continue
                elif not user_input:
                    print("Please enter a message or command")
                    continue

                # Send request to API
                success = self.send_request(user_input)

                if not success:
                    print("\n⚠️ Request failed. Try again or type /help for commands")

            except KeyboardInterrupt:
                print("\n\n👋 Interrupted by user. Goodbye!")
                break
            except EOFError:
                print("\n\n👋 End of input. Goodbye!")
                break


def main():
    """Main function"""
    # Check if custom URL provided
    base_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
        print(f"Using custom API URL: {base_url}")

    # Create and run tester
    tester = AgreeUponTester(base_url)
    tester.run()


if __name__ == "__main__":
    main()
