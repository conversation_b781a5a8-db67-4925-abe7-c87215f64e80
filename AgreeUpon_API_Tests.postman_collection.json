{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "AgreeUpon API Tests", "description": "Comprehensive test collection for the stateless AgreeUpon legal document drafting API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Initial NDA Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"I need to create an NDA agreement\",\n    \"agent_state\": null,\n    \"conversation_history\": [],\n    \"document\": \"\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Initial request to create an NDA agreement. Agent should identify document type and start collecting information."}}, {"name": "2. Provide Party Information", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"The parties are Alice Corp (disclosing party) and Bob LLC (receiving party)\",\n    \"agent_state\": {\n        \"document_type\": \"NDA\",\n        \"needed_fields\": {},\n        \"draft\": \"\",\n        \"is_drafted\": false,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [\n        {\n            \"sender\": \"user\",\n            \"content\": \"I need to create an NDA agreement\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"I'll help you create an NDA agreement. To get started, I need some basic information. Who are the parties involved in this agreement?\"\n        }\n    ],\n    \"document\": \"\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Provide the parties involved in the NDA. Agent should collect this information and ask for more details."}}, {"name": "3. Provide Additional Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"The effective date is January 1, 2024, confidentiality period is 5 years, and this covers technical information and business plans. Please draft the NDA now.\",\n    \"agent_state\": {\n        \"document_type\": \"NDA\",\n        \"needed_fields\": {\n            \"Disclosing Party\": \"Alice Corp\",\n            \"Receiving Party\": \"Bob LLC\"\n        },\n        \"draft\": \"\",\n        \"is_drafted\": false,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [\n        {\n            \"sender\": \"user\",\n            \"content\": \"I need to create an NDA agreement\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"I'll help you create an NDA agreement. To get started, I need some basic information. Who are the parties involved in this agreement?\"\n        },\n        {\n            \"sender\": \"user\",\n            \"content\": \"The parties are Alice Corp (disclosing party) and Bob LLC (receiving party)\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"Great! I have Alice Corp as the disclosing party and Bob LLC as the receiving party. Now I need a few more details: What is the effective date for this agreement? What should be the confidentiality period? What type of information will be covered?\"\n        }\n    ],\n    \"document\": \"\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Provide remaining details and request document generation. Agent should draft the complete NDA."}}, {"name": "4. Document Modification Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"Please add a clause about return of confidential materials upon termination of the agreement\",\n    \"agent_state\": {\n        \"document_type\": \"NDA\",\n        \"needed_fields\": {\n            \"Disclosing Party\": \"Alice Corp\",\n            \"Receiving Party\": \"Bob LLC\",\n            \"Effective Date\": \"January 1, 2024\",\n            \"Confidentiality Period\": \"5 years\",\n            \"Information Type\": \"technical information and business plans\"\n        },\n        \"draft\": \"NON-DISCLOSURE AGREEMENT\\n\\nThis Non-Disclosure Agreement (\\\"Agreement\\\") is entered into on January 1, 2024 by and between:\\n\\nDisclosing Party: Alice Corp\\nReceiving Party: Bob LLC\\n\\n1. CONFIDENTIAL INFORMATION\\nFor purposes of this Agreement, \\\"Confidential Information\\\" means technical information and business plans disclosed by the Disclosing Party.\\n\\n2. OBLIGATIONS\\nThe Receiving Party agrees to maintain the confidentiality of all Confidential Information for a period of 5 years.\\n\\n3. TERM\\nThis Agreement shall remain in effect for 5 years from the effective date.\",\n        \"is_drafted\": true,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [\n        {\n            \"sender\": \"user\",\n            \"content\": \"I need to create an NDA agreement\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"I'll help you create an NDA agreement. To get started, I need some basic information. Who are the parties involved in this agreement?\"\n        },\n        {\n            \"sender\": \"user\",\n            \"content\": \"The parties are Alice Corp (disclosing party) and Bob LLC (receiving party)\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"Great! I have Alice Corp as the disclosing party and Bob LLC as the receiving party. Now I need a few more details: What is the effective date for this agreement? What should be the confidentiality period? What type of information will be covered?\"\n        },\n        {\n            \"sender\": \"user\",\n            \"content\": \"The effective date is January 1, 2024, confidentiality period is 5 years, and this covers technical information and business plans. Please draft the NDA now.\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"Perfect! I have all the necessary information. I'll now draft your NDA agreement.\"\n        }\n    ],\n    \"document\": \"NON-DISCLOSURE AGREEMENT\\n\\nThis Non-Disclosure Agreement (\\\"Agreement\\\") is entered into on January 1, 2024 by and between:\\n\\nDisclosing Party: Alice Corp\\nReceiving Party: Bob LLC\\n\\n1. CONFIDENTIAL INFORMATION\\nFor purposes of this Agreement, \\\"Confidential Information\\\" means technical information and business plans disclosed by the Disclosing Party.\\n\\n2. OBLIGATIONS\\nThe Receiving Party agrees to maintain the confidentiality of all Confidential Information for a period of 5 years.\\n\\n3. TERM\\nThis Agreement shall remain in effect for 5 years from the effective date.\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Request modification to existing document. Agent should update the document with the new clause."}}, {"name": "5. Review Existing Document", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"Please review this existing NDA and suggest any improvements\",\n    \"agent_state\": {\n        \"document_type\": \"NDA\",\n        \"needed_fields\": {\n            \"Party A\": \"TechCorp Inc\",\n            \"Party B\": \"StartupXYZ LLC\"\n        },\n        \"draft\": \"CONFIDENTIALITY AGREEMENT\\n\\nThis agreement is between TechCorp Inc and StartupXYZ LLC.\\n\\nBoth parties agree to keep information confidential.\\n\\nThis agreement is effective immediately.\",\n        \"is_drafted\": true,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [],\n    \"document\": \"CONFIDENTIALITY AGREEMENT\\n\\nThis agreement is between TechCorp Inc and StartupXYZ LLC.\\n\\nBoth parties agree to keep information confidential.\\n\\nThis agreement is effective immediately.\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Review an existing document and provide suggestions for improvement."}}, {"name": "6. Create Lease Agreement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"I need to create a residential lease agreement for a property at 123 Main Street, Toronto\",\n    \"agent_state\": null,\n    \"conversation_history\": [],\n    \"document\": \"\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Start creating a lease agreement. Agent should identify document type and collect required information."}}, {"name": "7. Lease Agreement Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"The landlord is <PERSON>, tenant is <PERSON>, monthly rent is $2000, lease term is 12 months starting February 1, 2024, security deposit is $2000\",\n    \"agent_state\": {\n        \"document_type\": \"Lease Agreement\",\n        \"needed_fields\": {\n            \"Property Address\": \"123 Main Street, Toronto\"\n        },\n        \"draft\": \"\",\n        \"is_drafted\": false,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [\n        {\n            \"sender\": \"user\",\n            \"content\": \"I need to create a residential lease agreement for a property at 123 Main Street, Toronto\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"I'll help you create a residential lease agreement for 123 Main Street, Toronto. I need some additional information: Who is the landlord and tenant? What is the monthly rent amount? What is the lease term and start date? Is there a security deposit?\"\n        }\n    ],\n    \"document\": \"\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Provide lease agreement details. Agent should collect information and potentially draft the document."}}, {"name": "8. Partnership Agreement Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"I want to create a partnership agreement between myself and my business partner for our consulting firm\",\n    \"agent_state\": null,\n    \"conversation_history\": [],\n    \"document\": \"\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Request to create a partnership agreement. Agent should identify document type and start collecting information."}}, {"name": "9. Document with Placeholders Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"Please check if this document has any placeholders that need to be filled\",\n    \"agent_state\": {\n        \"document_type\": \"NDA\",\n        \"needed_fields\": {},\n        \"draft\": \"NON-DISCLOSURE AGREEMENT\\n\\nThis Agreement is between [PARTY_A] and [PARTY_B].\\n\\nEffective Date: [DATE]\\n\\nConfidential information includes [INFORMATION_TYPE].\\n\\nThis agreement expires on [EXPIRY_DATE].\",\n        \"is_drafted\": true,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [],\n    \"document\": \"NON-DISCLOSURE AGREEMENT\\n\\nThis Agreement is between [PARTY_A] and [PARTY_B].\\n\\nEffective Date: [DATE]\\n\\nConfidential information includes [INFORMATION_TYPE].\\n\\nThis agreement expires on [EXPIRY_DATE].\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Test placeholder detection functionality. Agent should identify unfilled placeholders."}}, {"name": "10. Complex Document Modification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_input\": \"Please modify this NDA to include a non-compete clause and change the confidentiality period to 3 years\",\n    \"agent_state\": {\n        \"document_type\": \"NDA\",\n        \"needed_fields\": {\n            \"Disclosing Party\": \"MegaCorp Ltd\",\n            \"Receiving Party\": \"Consultant Inc\",\n            \"Effective Date\": \"March 1, 2024\",\n            \"Confidentiality Period\": \"5 years\",\n            \"Information Type\": \"proprietary software and client lists\"\n        },\n        \"draft\": \"NON-DISCLOSURE AGREEMENT\\n\\nThis Non-Disclosure Agreement is entered into on March 1, 2024 between MegaCorp Ltd (Disclosing Party) and Consultant Inc (Receiving Party).\\n\\n1. CONFIDENTIAL INFORMATION\\nConfidential Information includes proprietary software and client lists.\\n\\n2. CONFIDENTIALITY OBLIGATIONS\\nReceiving Party shall maintain confidentiality for 5 years.\\n\\n3. PERMITTED DISCLOSURES\\nInformation may be disclosed if required by law.\\n\\n4. TERM\\nThis Agreement shall remain in effect for 5 years.\",\n        \"is_drafted\": true,\n        \"missing_prompt_count\": 0\n    },\n    \"conversation_history\": [\n        {\n            \"sender\": \"user\",\n            \"content\": \"I have an existing NDA that needs some modifications\"\n        },\n        {\n            \"sender\": \"assistant\",\n            \"content\": \"I'd be happy to help you modify your existing NDA. What specific changes would you like to make?\"\n        }\n    ],\n    \"document\": \"NON-DISCLOSURE AGREEMENT\\n\\nThis Non-Disclosure Agreement is entered into on March 1, 2024 between MegaCorp Ltd (Disclosing Party) and Consultant Inc (Receiving Party).\\n\\n1. CONFIDENTIAL INFORMATION\\nConfidential Information includes proprietary software and client lists.\\n\\n2. CONFIDENTIALITY OBLIGATIONS\\nReceiving Party shall maintain confidentiality for 5 years.\\n\\n3. PERMITTED DISCLOSURES\\nInformation may be disclosed if required by law.\\n\\n4. TERM\\nThis Agreement shall remain in effect for 5 years.\"\n}"}, "url": {"raw": "{{base_url}}/agent/chat", "host": ["{{base_url}}"], "path": ["agent", "chat"]}, "description": "Complex document modification request with multiple changes."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('agent_reply');", "    pm.expect(jsonData).to.have.property('agent_state');", "    pm.expect(jsonData).to.have.property('conversation_history');", "    pm.expect(jsonData).to.have.property('document');", "});", "", "pm.test(\"Agent reply is not empty\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.agent_reply).to.not.be.empty;", "});", "", "pm.test(\"Agent state is valid\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.agent_state).to.be.an('object');", "    pm.expect(jsonData.agent_state).to.have.property('document_type');", "    pm.expect(jsonData.agent_state).to.have.property('needed_fields');", "    pm.expect(jsonData.agent_state).to.have.property('is_drafted');", "});", "", "pm.test(\"Conversation history is array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.conversation_history).to.be.an('array');", "});", "", "// Log response for debugging", "console.log('Agent Reply:', pm.response.json().agent_reply);", "console.log('Document Type:', pm.response.json().agent_state.document_type);", "console.log('Is Drafted:', pm.response.json().agent_state.is_drafted);", "console.log('Document Length:', pm.response.json().document.length);"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}]}