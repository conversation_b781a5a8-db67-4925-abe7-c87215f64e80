"""
Shared helper utilities for the legal-assistant agent.

• invoke_with_retry        – resilient LLM / chain invocation
• _clean_llm_text          – strips <think> blocks, markdown fences, whitespace
• _first_json_block        – extracts first {...} that contains a key
• safe_parse_json_block    – tolerant JSON→dict loader
• salvage_json             – multi-strategy JSON extraction fallback
• detect_placeholders      – finds tokens like [DATE], [NAME]
• strip_llm_fluff          – removes “Here is …” boilerplate
• is_finalization_command  – simplistic “sign-off” detector
"""

from __future__ import annotations

import ast
import json
import logging
import re
import time
from functools import lru_cache
from typing import Any, Dict, List, Optional

logger = logging.getLogger("agent.utils")
logger.setLevel(logging.DEBUG)

# Performance tracking
_performance_stats = {
    "total_calls": 0,
    "successful_calls": 0,
    "failed_calls": 0,
    "avg_response_time": 0.0,
}


def _update_avg_response_time(response_time: float):
    """Update average response time with exponential moving average."""
    alpha = 0.1  # Smoothing factor
    if _performance_stats["avg_response_time"] == 0.0:
        _performance_stats["avg_response_time"] = response_time
    else:
        _performance_stats["avg_response_time"] = (
            alpha * response_time
            + (1 - alpha) * _performance_stats["avg_response_time"]
        )


def get_performance_stats() -> Dict[str, Any]:
    """Get current performance statistics."""
    return _performance_stats.copy()


# ────────────────────────────────────────────────────────────
# Retry wrapper
# ────────────────────────────────────────────────────────────
def invoke_with_retry(
    chain_or_runnable,
    inputs: Dict[str, Any],
    max_retries: int = 2,
):
    """
    Optimized invoke with performance tracking, intelligent retry, and fallback support.
    """
    start_time = time.time()
    original_chain = chain_or_runnable

    # Update performance stats
    _performance_stats["total_calls"] += 1

    for attempt in range(1, max_retries + 1):
        try:
            # Invoke the chain/runnable
            if hasattr(chain_or_runnable, "invoke"):
                result = chain_or_runnable.invoke(inputs)
            elif hasattr(chain_or_runnable, "run"):
                if isinstance(inputs, dict):
                    result = chain_or_runnable.run(**inputs)
                else:
                    result = chain_or_runnable.run(inputs)
            else:
                result = chain_or_runnable(inputs)

            # Track successful call
            response_time = time.time() - start_time
            _performance_stats["successful_calls"] += 1
            _update_avg_response_time(response_time)

            if response_time > 10.0:  # Log slow responses
                logger.warning(f"Slow response: {response_time:.2f}s")

            return result

        except Exception as exc:
            _performance_stats["failed_calls"] += 1
            # Retry logic only for transient HTTP 503 / rate-limit style errors
            transient = False
            msg = str(exc).lower()
            if "status code: 503" in msg or "503" in msg:
                transient = True
            if "rate limit" in msg or "temporarily unavailable" in msg:
                transient = True

            if not transient:
                raise  # propagate other errors immediately

            logger.warning(
                "⚠️ invoke_with_retry (%d/%d transient) failed: %s",
                attempt,
                max_retries,
                exc,
            )
            if attempt == max_retries:
                # Check if we can fallback to DeepSeek
                if _should_fallback_to_deepseek(original_chain):
                    logger.warning("🔄 Primary LLM failed, trying DeepSeek fallback...")
                    return _try_deepseek_fallback(original_chain, inputs)
                raise
            # Exponential backoff: 10s * attempt
            time.sleep(10 * attempt)


def _should_fallback_to_deepseek(chain_or_runnable) -> bool:
    """Check if we should fallback to DeepSeek for this chain."""
    from agent.llm import TOGGLE_LLM, llm_chain

    # Fallback to DeepSeek if:
    # 1. TOGGLE_LLM is True and chain uses primary LLM (normal fallback)
    # 2. TOGGLE_LLM is False and chain uses primary LLM (should have been using DeepSeek)
    if hasattr(chain_or_runnable, "llm") and chain_or_runnable.llm is llm_chain:
        return True  # Always fallback when primary LLM fails
    return False


def _try_deepseek_fallback(original_chain, inputs):
    """Try to create a DeepSeek version of the chain and invoke it."""
    try:
        from agent.llm import llm_chain_deepseek

        # Create a copy of the chain with DeepSeek LLM
        if hasattr(original_chain, "llm") and hasattr(original_chain, "prompt"):
            # For LLMChain objects, create a new one with DeepSeek
            from langchain.chains import LLMChain

            fallback_chain = LLMChain(
                llm=llm_chain_deepseek,
                prompt=original_chain.prompt,
                output_key=getattr(original_chain, "output_key", "text"),
                verbose=getattr(original_chain, "verbose", False),
            )

            # Copy memory if it exists
            if hasattr(original_chain, "memory") and original_chain.memory:
                fallback_chain.memory = original_chain.memory
                fallback_chain.input_key = getattr(
                    original_chain, "input_key", "user_input"
                )

            logger.info("✅ Created DeepSeek fallback chain")
            return fallback_chain.invoke(inputs)
        else:
            logger.warning("⚠️ Cannot create fallback for this chain type")
            raise Exception("Fallback not supported for this chain type")

    except Exception as e:
        logger.error(f"❌ DeepSeek fallback also failed: {e}")
        raise


# ────────────────────────────────────────────────────────────
# Text-cleanup helpers
# ────────────────────────────────────────────────────────────
_THINK_END_RE = re.compile(r"</think>", re.IGNORECASE)
_CODE_FENCE_START_RE = re.compile(r"^\s*```[a-zA-Z0-9_-]*\s*")
_CODE_FENCE_END_RE = re.compile(r"\s*```\s*$")


@lru_cache(maxsize=128)
def _clean_llm_text(text: str) -> str:
    """
    Strip <think> … </think> blocks *and* leading / trailing code fences.
    Cached for performance optimization.
    """
    m = list(_THINK_END_RE.finditer(text))
    if m:
        text = text[m[-1].end() :]

    text = _CODE_FENCE_START_RE.sub("", text)
    text = _CODE_FENCE_END_RE.sub("", text)
    return text.strip()


# ────────────────────────────────────────────────────────────
# Balanced-brace JSON extraction helpers
# ────────────────────────────────────────────────────────────
def _iterate_json_candidates(text: str):
    depth = 0
    start = None
    in_str = False
    escape = False

    for i, ch in enumerate(text):
        if ch == '"' and not escape:
            in_str = not in_str
        escape = ch == "\\" and not escape

        if in_str:
            continue

        if ch == "{":
            if depth == 0:
                start = i
            depth += 1
        elif ch == "}":
            depth -= 1
            if depth == 0 and start is not None:
                yield text[start : i + 1]
                start = None


_SINGLE_TO_DOUBLE_RE = re.compile(r"'([^']+?)'")


@lru_cache(maxsize=64)
def safe_parse_json_block(block: str) -> Optional[Dict[str, Any]]:
    try:
        return json.loads(block)
    except json.JSONDecodeError:
        pass

    try:
        converted = _SINGLE_TO_DOUBLE_RE.sub(r'"\1"', block)
        return json.loads(converted)
    except json.JSONDecodeError:
        pass

    try:
        return ast.literal_eval(block)
    except Exception:
        return None


# ────────────────────────────────────────────────────────────
# Fallback JSON-extraction strategies
# ────────────────────────────────────────────────────────────
_JSON_FENCE_RE = re.compile(
    r"```json\s*({[\s\S]+?})\s*```",
    re.IGNORECASE,
)


def salvage_json(
    text: str, required_key: str = "destination"
) -> Optional[Dict[str, Any]]:
    """
    Robust, multi-strategy JSON extraction.

    • `required_key` – only return a dict that contains this key
                       (defaults to "destination" for router output).
    """
    # 1) ```json … ``` fenced blocks
    for m in _JSON_FENCE_RE.finditer(text):
        block = m.group(1)
        parsed = safe_parse_json_block(block)
        if parsed and required_key in parsed:
            logger.debug("🛟 Salvaged JSON from fenced block.")
            return parsed

    # 2) Any balanced { … } group
    for block in _iterate_json_candidates(text):
        if f'"{required_key}"' not in block and f"'{required_key}'" not in block:
            continue
        parsed = safe_parse_json_block(block)
        if parsed and required_key in parsed:
            logger.debug("🛟 Salvaged JSON from balanced scan.")
            return parsed

    # 3) Reverse scan – last brace group containing the key
    idx = text.rfind("}")
    while idx != -1:
        start = text.rfind("{", 0, idx)
        if start == -1:
            break
        block = text[start : idx + 1]
        if f'"{required_key}"' in block or f"'{required_key}'" in block:
            parsed = safe_parse_json_block(block)
            if parsed and required_key in parsed:
                logger.debug("🛟 Salvaged JSON from reverse scan.")
                return parsed
        idx = text.rfind("}", 0, start)

    return None


# ────────────────────────────────────────────────────────────
# Misc helpers
# ────────────────────────────────────────────────────────────
_PLACEHOLDER_RE = re.compile(r"\[[A-Z0-9_]+\]")


def detect_placeholders(doc: str) -> List[str]:
    return list(dict.fromkeys(_PLACEHOLDER_RE.findall(doc)))


_FLUFF_RE = re.compile(
    r"^\s*(Here is|Below is|Sure[,:\-]?|Certainly[,:\-]?|Here's the)\b[^\n]*\n+",
    re.IGNORECASE,
)


def strip_llm_fluff(text: str) -> str:
    return _FLUFF_RE.sub("", text).strip()


_FINAL_CMD_RE = re.compile(
    r"\b(finalise|finalize|looks\s+good|approved|no\s+further\s+changes)\b",
    re.IGNORECASE,
)


def is_finalization_command(user_input: str) -> bool:
    return bool(_FINAL_CMD_RE.search(user_input))
