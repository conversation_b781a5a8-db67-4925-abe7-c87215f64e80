"""
api/routers/agent.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Simplified stateless agent API endpoint.
"""

import logging
import json
from fastapi import APIRouter, HTTPException, Header, Request
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

from agent.agent_runner import run_agent_step
from agent.state import AgentState
from api.services.agreement_draft_service import AgreementDraftService

router = APIRouter(prefix="/agent", tags=["agent"])
logger = logging.getLogger("api.routers.agent")
logger.setLevel(logging.INFO)

# Add console handler if not already present
if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)


# ──────────────────────────────────────────────
# Pydantic Models for API
# ──────────────────────────────────────────────


class AgentRequest(BaseModel):
    user_input: str
    agent_state: Optional[Dict[str, Any]] = None
    conversation_history: Optional[List[Dict[str, str]]] = None
    document: Optional[str] = ""


class AgentResponse(BaseModel):
    agent_reply: str
    agent_state: Dict[str, Any]
    conversation_history: List[Dict[str, str]]
    document: str


# ──────────────────────────────────────────────
# POST /agent/chat
# ──────────────────────────────────────────────
@router.post("/chat", response_model=AgentResponse)
async def chat_with_agent(request: AgentRequest, raw_request: Request, session_id: Optional[str] = Header(None, alias="session_id")):
    """
    Stateless agent chat endpoint.

    Accepts:
    - user_input: The user's message
    - agent_state: Current agent state (optional, defaults to empty state)
    - conversation_history: List of previous messages (optional)
    - document: Current document content (optional)

    Returns:
    - agent_reply: The agent's response
    - updated_agent_state: Updated agent state
    - updated_conversation_history: Updated conversation history
    - document: Generated or updated document (empty string if none)
    """
    try:
        # Log session_id from header
        logger.info(f"Session ID: {session_id}")

        # Initialize agent state
        if request.agent_state:
            state = AgentState(**request.agent_state)
        else:
            state = AgentState()

        # Sync document content with agent state
        if request.document:
            # If document is provided, update state with it
            state.draft = request.document
            state.is_drafted = bool(request.document.strip())
        elif state.draft and not request.document:
            # If state has a document but request doesn't, keep state document
            pass
        else:
            # Ensure consistency - if no document in either, make sure state reflects that
            if not state.draft:
                state.is_drafted = False

        # Run agent step
        result = run_agent_step(
            state=state,
            user_input=request.user_input,
            conversation_history=request.conversation_history or [],
        )

        tmp = result["updated_state"].dict()
        logger.error(f"is_drafted: {tmp['is_drafted']}")
        
        # Persist draft document to database if it exists and is_drafted is True
        if result["draft_document"] and tmp['is_drafted']:
            try:
                draft_service = AgreementDraftService()
                
                # Extract user ID from headers if available
                usr_id = raw_request.headers.get("usr_id")
                usr_id = int(usr_id) if usr_id and usr_id.isdigit() else None
                
                # Convert session_id to int if available
                session_id_int = int(session_id) if session_id and session_id.isdigit() else None
                
                # Check if entry with session_id exists
                existing_drafts = []
                if session_id_int:
                    existing_drafts = await draft_service.get_drafts_by_session(session_id_int)
                    #logger.info(f"existing_drafts: {existing_drafts}")
                
                if existing_drafts:
                    # Update the most recent draft (last in list)
                    latest_draft = existing_drafts[-1]
                    updated_draft = await draft_service.update_draft_content(
                        draft_id=latest_draft.draft_id,
                        content=result["draft_document"]
                    )
                    #logger.info(f"Draft updated in database with ID: {updated_draft.draft_id}")
                else:
                    # Save new draft to database
                    saved_draft = await draft_service.save_draft(
                        content=result["draft_document"],
                        usr_id=usr_id,
                        session_id=session_id_int
                    )
                    #logger.info(f"Draft saved to database with ID: {saved_draft.draft_id}")
                
            except Exception as db_error:
                # Log error but don't fail the request
                #logger.error(f"Failed to save/update draft to database: {str(db_error)}")
                pass

        # Return response
        return AgentResponse(
            agent_reply=result["reply"],
            agent_state=result["updated_state"].dict(),
            conversation_history=result["updated_conversation_history"],
            document=result["draft_document"],
        )

    except Exception as e:
        logger.error(f"Error in agent chat: {e}")
        raise HTTPException(status_code=500, detail=f"Agent processing error: {str(e)}")
