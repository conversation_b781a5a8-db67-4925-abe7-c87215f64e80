from tortoise import fields
from tortoise.models import Model
from datetime import datetime


class AgreementDraft(Model):
    draft_id = fields.BigIntField(pk=True)
    usr_id = fields.BigIntField(null=True)
    session_id = fields.BigIntField(null=True)
    content = fields.TextField(null=True)
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "agreement_draft"
