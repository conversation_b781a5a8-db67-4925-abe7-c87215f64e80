/* Reset & base */
* { box-sizing:border-box; margin:0; padding:0; }
body { font-family: 'Segoe UI', sans-serif; height:100vh; }

/* Overlay for auth */
.overlay {
  position:fixed; top:0; left:0; width:100%; height:100%;
  background:rgba(0,0,0,0.5); display:flex; align-items:center; justify-content:center;
}
.auth-box {
  background:#fff; padding:2rem; border-radius:8px; width:300px; text-align:center;
  box-shadow:0 4px 12px rgba(0,0,0,0.15);
}
.auth-form input {
  width:100%; padding:0.5rem; margin:0.5rem 0; border:1px solid #ccc; border-radius:4px;
}
.auth-form button {
  width:100%; padding:0.5rem; border:none; background:#0078D4; color:#fff;
  border-radius:4px; cursor:pointer; font-size:1rem;
}
.auth-form p { margin-top:0.5rem; font-size:0.9rem; }
.auth-form a { color:#0078D4; text-decoration:none; }
.hidden { display:none; }

/* Main app layout */
#app { display:flex; height:100%; }
#sidebar {
  width:20%; background:#f5f5f5; padding:1rem; overflow-y:auto;
  border-right:1px solid #ddd;
}
#sidebar button {
  width:100%; padding:0.5rem; margin-bottom:1rem;
  border:none; background:#0078D4; color:#fff; border-radius:4px; cursor:pointer;
}
#convList { list-style:none; }
#convList li {
  padding:0.5rem; margin:0.25rem 0; cursor:pointer; border-radius:4px;
}
#convList li.active, #convList li:hover { background:#e1f0ff; }

/* Chat pane */
#chat {
  width:40%; display:flex; flex-direction:column; padding:1rem;
}
#messages {
  flex:1; border:1px solid #ddd; border-radius:4px; padding:1rem;
  overflow-y:auto; background:#fff;
}
.input-row {
  display:flex; margin-top:0.5rem;
}
.input-row input {
  flex:1; padding:0.5rem; border:1px solid #ccc; border-radius:4px 0 0 4px;
}
.input-row button {
  padding:0.5rem 1rem; border:none; background:#0078D4; color:#fff;
  border-radius:0 4px 4px 0; cursor:pointer;
}
.message { margin:0.5rem 0; max-width:80%; }
.user { align-self:flex-end; background:#0078D4; color:#fff; padding:0.5rem; border-radius:4px; }
.assistant { align-self:flex-start; background:#ececec; padding:0.5rem; border-radius:4px; }

/* Doc pane */
#doc {
  width:40%; padding:1rem; border-left:1px solid #ddd; overflow-y:auto;
  background:#fafafa;
}
#doc h3 { margin-bottom:0.5rem; }
#documentContent { white-space:pre-wrap; background:#fff; padding:1rem; border-radius:4px; border:1px solid #ddd; }
