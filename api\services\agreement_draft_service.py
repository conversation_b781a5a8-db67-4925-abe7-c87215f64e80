"""
Agreement Draft Service
Handles business logic for agreement draft operations
"""

import logging
from typing import Optional
from api.models import AgreementDraft
from api.repositories.agreement_draft_repository import AgreementDraftRepository

logger = logging.getLogger(__name__)


class AgreementDraftService:
    """Service layer for agreement draft operations"""
    
    def __init__(self):
        self.repository = AgreementDraftRepository()
    
    async def save_draft(
        self, 
        content: str, 
        usr_id: Optional[int] = None, 
        session_id: Optional[int] = None
    ) -> AgreementDraft:
        """
        Save a new agreement draft
        
        Args:
            content: The draft content
            usr_id: User ID (optional)
            session_id: Session ID (optional)
            
        Returns:
            AgreementDraft: The saved draft object
        """
        try:
            # Validate content
            if not content or not content.strip():
                raise ValueError("Draft content cannot be empty")
            
            # Save through repository
            draft = await self.repository.create_draft(
                content=content.strip(),
                usr_id=usr_id,
                session_id=session_id
            )
            
            logger.info(f"Draft saved successfully with ID: {draft.draft_id}")
            return draft
            
        except Exception as e:
            logger.error(f"Error saving draft: {str(e)}")
            raise
    
    async def get_draft_by_id(self, draft_id: int) -> Optional[AgreementDraft]:
        """
        Get draft by ID
        
        Args:
            draft_id: The draft ID
            
        Returns:
            AgreementDraft or None if not found
        """
        try:
            return await self.repository.get_by_id(draft_id)
        except Exception as e:
            logger.error(f"Error retrieving draft {draft_id}: {str(e)}")
            raise
    
    async def get_drafts_by_session(self, session_id: int) -> list[AgreementDraft]:
        """
        Get all drafts for a session
        
        Args:
            session_id: The session ID
            
        Returns:
            List of AgreementDraft objects
        """
        try:
            return await self.repository.get_by_session_id(session_id)
        except Exception as e:
            logger.error(f"Error retrieving drafts for session {session_id}: {str(e)}")
            raise
    
    async def update_draft_content(self, draft_id: int, content: str) -> Optional[AgreementDraft]:
        """
        Update draft content
        
        Args:
            draft_id: The draft ID
            content: New content
            
        Returns:
            Updated AgreementDraft or None if not found
        """
        try:
            if not content or not content.strip():
                raise ValueError("Draft content cannot be empty")
                
            return await self.repository.update_content(draft_id, content.strip())
        except Exception as e:
            logger.error(f"Error updating draft {draft_id}: {str(e)}")
            raise
