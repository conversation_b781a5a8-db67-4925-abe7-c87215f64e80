# Agree Upon Frontend

A modern, responsive web interface for the Agree Upon Legal Drafting Assistant.

## Features

- **Clean, Modern Design**: Professional interface with gradient backgrounds and smooth animations
- **Real-time Chat**: Interactive chat interface with the AI legal assistant
- **Document Generation**: Live document preview and editing capabilities
- **Responsive Layout**: Works seamlessly on desktop, tablet, and mobile devices
- **Document Actions**: Copy and download generated legal documents
- **Session Management**: Stateless operation with session persistence

## Interface Components

### Header
- Application title and branding
- New Chat button to start fresh conversations
- Clear All button to reset the session

### Chat Section
- Welcome message with example prompts
- Real-time message exchange with the AI assistant
- Auto-resizing text input with keyboard shortcuts
- Loading states and error handling

### Document Section
- Live preview of generated legal documents
- Copy to clipboard functionality
- Download as text file
- Professional monospace font for document display

## Usage

1. **Starting a Conversation**: Click on any example prompt or type your own message
2. **Document Creation**: Ask the AI to create specific legal documents (NDA, lease agreement, etc.)
3. **Document Management**: Use the copy and download buttons to save your documents
4. **New Sessions**: Use "New Chat" to start over with a fresh conversation

## Technical Details

- **Frontend**: Pure HTML, CSS, and JavaScript (no frameworks)
- **API Integration**: Connects to `/agent/chat` endpoint
- **State Management**: Maintains conversation history and agent state
- **Responsive Design**: CSS Grid and Flexbox for layout
- **Accessibility**: Keyboard navigation and screen reader friendly

## Keyboard Shortcuts

- **Enter**: Send message
- **Shift + Enter**: New line in message input
- **Escape**: Clear current input

## Browser Support

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Customization

The interface can be customized by modifying:
- `static/index.html`: Structure and JavaScript functionality
- `static/style.css`: Additional styling and animations
- Color scheme variables in the CSS for branding changes
