/* Additional styles for the legal document assistant */
/* Note: Main styles are now in index.html for better organization */

/* Enhanced animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message {
  animation: slideIn 0.3s ease-out;
}

/* Enhanced button hover effects */
.send-button:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
}

.document-actions button:hover {
  transform: translateY(-1px) scale(1.05);
}

/* Enhanced scrollbar for webkit browsers */
.chat-container::-webkit-scrollbar,
.document-content::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track,
.document-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb,
.document-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover,
.document-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus states */
.message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Loading animation for messages */
.message.loading::after {
  content: "...";
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%,
  20% {
    content: ".";
  }
  40% {
    content: "..";
  }
  60%,
  100% {
    content: "...";
  }
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 0.9em;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification.success {
  background: rgba(72, 187, 120, 0.95);
  color: white;
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.notification.error {
  background: rgba(245, 101, 101, 0.95);
  color: white;
  border: 1px solid rgba(245, 101, 101, 0.3);
}

.notification.info {
  background: rgba(102, 126, 234, 0.95);
  color: white;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .header {
    padding: 10px 20px;
  }

  .header h1 {
    font-size: 1.2em;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .message {
    max-width: 90%;
  }

  .document-actions {
    flex-direction: column;
  }

  .notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}
