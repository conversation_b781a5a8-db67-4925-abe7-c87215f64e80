# Agree Upon – Stateless <PERSON>hain + FastAPI Agentic AI Legal Drafter

A stateless, LangChain-native conversational legal document drafting assistant using FastAPI and OpenAI-compatible LLM.

## Features
- Conversational agent for legal document drafting (NDA, Lease, Partnership, Shareholder, etc.)
- Gathers required fields, generates clean drafts, and allows iterative refinement
- No LLM fluff or placeholders in final drafts
- **Stateless operation** - no database or authentication required
- Conversation memory with Lang<PERSON>hain `ConversationBufferMemory`
- OpenAI-compatible LLM with 503 cold-start retry
- Simple FastAPI endpoint for agent chat
- Ready for future RAG, web search, doc upload/parse

## Folder Structure
```
agree_upon/
├── api/
│   ├── main.py
│   └── routers/
│       └── agent.py
├── agent/
│   ├── prompts.py
│   ├── memory.py
│   ├── utils.py
│   ├── agent_runner.py
│   ├── state.py
│   └── chains/
│       ├── conversational_legal_chain.py
│       ├── document_drafter_chain.py
│       └── placeholder_checker.py
├── static/
│   └── index.html
├── .env
├── requirements.txt
├── test_api.py
└── README.md
```

## API Endpoint

### POST /agent/chat

**Input JSON:**
```json
{
    "user_input": "string",
    "agent_state": {}, // optional, JSON object
    "conversation_history": [], // optional, array of {sender: "user"|"assistant", content: "string"}
    "document": "" // optional, multiline string, empty if no document
}
```

**Output JSON:**
```json
{
    "agent_reply": "string",
    "updated_agent_state": {}, // JSON object
    "updated_conversation_history": [], // array of {sender: "user"|"assistant", content: "string"}
    "document": "" // multiline string, empty if no document generated/modified
}
```

## Quickstart
1. Install dependencies: `pip install -r requirements.txt`
2. Set your OpenRouter API key in `.env` as `OPENROUTER_API_KEY=...`
3. Run FastAPI: `uvicorn api.main:app --reload`
4. Test the API: `python test_api.py`

## Example Usage

```python
import requests

# First message
response = requests.post("http://localhost:8000/agent/chat", json={
    "user_input": "I need to create an NDA agreement",
    "agent_state": None,
    "conversation_history": [],
    "document": ""
})

result = response.json()
print(result["agent_reply"])

# Follow-up message
response = requests.post("http://localhost:8000/agent/chat", json={
    "user_input": "The parties are Alice Corp and Bob LLC",
    "agent_state": result["updated_agent_state"],
    "conversation_history": result["updated_conversation_history"],
    "document": result["document"]
})
```

---

See code comments and docstrings for further details.
