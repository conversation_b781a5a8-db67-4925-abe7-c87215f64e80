"""
Detect unresolved placeholders in a draft.

Changes
• Returns structured list via PydanticOutputParser
"""

import logging
from typing import List
from pydantic import BaseModel, Field

from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain.output_parsers import PydanticOutputParser

from agent.llm import HybridLLM

# PLACEHOLDER_CHECKER_PROMPT inlined from agent/prompts.py
PLACEHOLDER_CHECKER_PROMPT = PromptTemplate.from_template(
    """
You're checking the draft below for any missing fields or placeholder values like [PLACEHOLDER], [Your Name], etc.

Draft:
{draft}

List all placeholders found in the draft. Return as a Python list. If none found, return an empty list.
"""
)

logger = logging.getLogger("agent.placeholder_checker")
logger.setLevel(logging.DEBUG)


# ────────────────────────────
# J<PERSON><PERSON> schema
# ────────────────────────────
class PlaceholderCheckOut(BaseModel):
    is_success: bool
    missing_desc: str
    ask_user: str


parser = PydanticOutputParser(pydantic_object=PlaceholderCheckOut)

# ────────────────────────────
# Prompt
# ────────────────────────────
# prompt for the main chain (unchanged, already in this file)
prompt = PromptTemplate(
    input_variables=["draft", "history"],
    template="""
Refer to the conversation history (you can also refer deep into this history to extract key details) to cross-check missing details.
Scan the draft below.

• If NO placeholders like [DATE] [NAME] [PARTY A]... etc remain:
  return {{"is_success": true, "missing_desc": "", "ask_user": ""}}

• Otherwise:
  return {{"is_success": false, "missing_desc": "<short English list of what’s missing>", "ask_user": "<single concise question to get all missing info>"}}

Return ONLY that JSON. No other text.

Conversation history:
{history}

Draft:
{draft}
""",
)


# ────────────────────────────
# Chain
# ────────────────────────────
def get_placeholder_checker_chain(
    memory: "StatelessBufferMemory | None" = None,
) -> LLMChain:
    # Configure chain based on whether memory is provided
    llmchain_kwargs = dict(
        llm=HybridLLM(),
        prompt=prompt.partial(placeholder_checker_prompt=PLACEHOLDER_CHECKER_PROMPT),
        output_key="text",  # raw text; we'll parse separately
        verbose=True,
    )

    # Only add memory and input_key if memory is provided
    if memory is not None:
        llmchain_kwargs["memory"] = memory
        llmchain_kwargs["input_key"] = "user_input"

    return LLMChain(**llmchain_kwargs)
